import aiohttp
import asyncio
import traceback
import pymongo
from lxml import etree


class UACPartsCrawler:

    def __init__(self, concurrency=5):
        self.headers = {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'no-cache',
            'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'origin': 'https://uacparts.com',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'sec-ch-ua': '"Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36',
            'x-requested-with': 'XMLHttpRequest'
        }
        self.session = aiohttp.ClientSession(headers=self.headers)
        self.semaphore = asyncio.Semaphore(concurrency)
        self.mongo_obj = pymongo.MongoClient("**************************************************************")

    async def fetch_with_semaphore(self, func, *args):
        async with self.semaphore:
            return await func(*args)

    async def get_all_years(self):
        url = "https://uacparts.com/VA_YearsAjaxDropdown/GetItems"
        payload = {
            "__aweconid": "VA_Years",
            "v": ""
        }
        async with self.session.post(url, data=payload) as response:
            year_list = await response.json()

        return [year_data for year_data in year_list if year_data['k']]

    async def get_make_list_by_year(self, year):
        url = "https://uacparts.com/VA_MakesByYearAjaxDropdown/GetItems"
        payload = {
            "__aweconid": "VA_MakesByYear",
            "v": "",
            "year": year['k']
        }

        async with self.session.post(url, data=payload) as response:
            make_list = await response.json()

        self.mongo_obj['UACParts0808']['MakeData'].insert_many([{
            "yearid": year['k'],
            "year": year['c'],
            "makeid": data['k'],
            "make": data['c'],
        } for data in make_list if data['k']])

        return [make_data for make_data in make_list if make_data['k']]

    async def get_model_by_year_make(self, year, make_id):
        url = "https://uacparts.com/VA_ModelsByYearMakeAjaxDropdown/GetItems"
        payload = {
            "__aweconid": "VA_ModelsByYearMake",
            "v": "",
            "year": year['k'],
            "make": make_id['k']
        }
        async with self.session.post(url, data=payload) as response:
            model_list = await response.json()

        self.mongo_obj['UACParts0808']['ModelData'].insert_many([{
            "yearid": year['k'],
            "year": year['c'],
            "makeid": make_id['k'],
            "make": make_id['c'],
            "modelid": data['k'],
            "model": data['c'],
        } for data in model_list if data['k']])

        return [model_data for model_data in model_list if model_data['k']]

    async def get_submodel_by_year_make_model(self, year, make_id, model_id):
        url = "https://uacparts.com/VA_SubmodelsByYearMakeModelAjaxDropdown/GetItems"
        payload = {
            "__aweconid": "VA_SubmodelsByYearMakeModel",
            "v": "",
            "year": year['k'],
            "make": make_id['k'],
            "model": model_id['k']
        }
        async with self.session.post(url, data=payload) as response:
            submodel_list = await response.json()

        self.mongo_obj['UACParts0808']['SubModelData'].insert_many([{
            "yearid": year['k'],
            "year": year['c'],
            "makeid": make_id['k'],
            "make": make_id['c'],
            "modelid": model_id['k'],
            "model": model_id['c'],
            "submodelid": data['k'],
            "submodel": data['c'],
        } for data in submodel_list if data['k']])

        return [submodel_data for submodel_data in submodel_list if submodel_data['k']]

    async def get_engines_by_year_make_model_submodel(self, year, make_id, model_id, submodel_id):
        url = "https://uacparts.com/VA_EnginesByYearMakeModelSubAjaxDropdown/GetItems"
        payload = {
            "__aweconid": "VA_EnginesByYearMakeModelSub",
            "v": "",
            "year": year['k'],
            "make": make_id['k'],
            "model": model_id['k'],
            "submodel": submodel_id['k']
        }
        async with self.session.post(url, data=payload) as response:
            engines_list = await response.json()

        self.mongo_obj['UACParts0808']['EngineData'].insert_many([{
            "yearid": year['k'],
            "year": year['c'],
            "makeid": make_id['k'],
            "make": make_id['c'],
            "modelid": model_id['k'],
            "model": model_id['c'],
            "submodelid": submodel_id['k'],
            "submodel": submodel_id['c'],
            "engineid": data['k'],
            "engine": data['c'],
        } for data in engines_list if data['k']])

        return [engine_data for engine_data in engines_list if engine_data['k']]

    async def get_vio_by_year_make_model_submodel_engine(self, year, make_id, model_id, submodel_id, engine_id):
        url = "https://uacparts.com/Catalog/GetAppVIO"
        payload = {
            "yearid": year['k'],
            "makeid": make_id['k'],
            "modelid": model_id['k'],
            "submodelid": submodel_id['k'],
            "engineid": engine_id['k']
        }
        async with self.session.post(url, data=payload) as response:
            vio_data = await response.json()

        temp_dict = {
            "yearid": year['k'],
            "year": year['c'],
            "makeid": make_id['k'],
            "make": make_id['c'],
            "modelid": model_id['k'],
            "model": model_id['c'],
            "submodelid": submodel_id['k'],
            "submodel": submodel_id['c'],
            "engineid": engine_id['k'],
            "engine": engine_id['c'],
            "vio": vio_data['VIO']
        }
        self.mongo_obj['UACParts0808']['VioData'].insert_one(temp_dict)

    async def get_part_detail_by_all_args(self, year, make_id, model_id, submodel_id, engine_id, vehicle_id):
        url = "https://uacparts.com/PartsAjaxList/Search"
        payload = {
            "__aweconid": "Parts",
            "year": year['k'],
            "make": make_id['k'],
            "model": model_id['k'],
            "submodel": submodel_id['k'],
            "engine": engine_id['k'],
            "page": 1
        }
        async with self.session.post(url, data=payload) as response:
            parts_data = await response.json()

        is_more = parts_data.get("d", {}).get("m", None)
        parts_html_str = parts_data.get("d", {}).get("c", "")
        result_list = []
        if parts_html_str:
            html = etree.HTML(parts_html_str, parser=etree.HTMLParser(encoding='utf-8'))
            for li_ele in html.xpath("//li"):
                part_label = li_ele.xpath(".//h2[@class='part-label']/text()")[0]
                img_list = li_ele.xpath(".//div[@class='part-image']/img/@src")
                part_desc = "\n".join(li_ele.xpath(".//div[@class='part-info']/div/text()"))
                result_list.append({
                    "vehicle_id": vehicle_id,
                    "part_label": part_label,
                    "img_list": img_list,
                    "part_desc": part_desc,
                    "is_more": is_more
                })

        self.mongo_obj['UACParts0808']['PartsData'].insert_many(result_list)

    async def close(self):
        await self.session.close()


async def main():
    crawler = UACPartsCrawler(concurrency=10)  # 设置并发数
    try:
        years_list = await crawler.get_all_years()
        print(f"获取到 {len(years_list)} 个年份")

        tasks = [crawler.fetch_with_semaphore(crawler.get_make_list_by_year, year) for year in years_list]
        makes_by_year = await asyncio.gather(*tasks)
        print("完成所有厂商数据抓取")

        make_tasks = []
        for year, makes in zip(years_list, makes_by_year):
            for make in makes:
                make_tasks.append(crawler.fetch_with_semaphore(crawler.get_model_by_year_make, year, make))
        models_by_make = await asyncio.gather(*make_tasks)
        print("完成所有Model数据抓取")

        submodel_tasks = []
        model_index = 0
        for year, makes in zip(years_list, makes_by_year):
            for make in makes:
                models = models_by_make[model_index]
                model_index += 1
                for model in models:
                    submodel_tasks.append(crawler.fetch_with_semaphore(crawler.get_submodel_by_year_make_model, year, make, model))
        submodels_by_model = await asyncio.gather(*submodel_tasks)
        print("完成所有SubModel的数据抓取")

        engine_tasks = []
        submodel_index = 0
        for year, makes in zip(years_list, makes_by_year):
            for make in makes:
                models = models_by_make[submodel_index]
                submodels = submodels_by_model[submodel_index]
                submodel_index += 1
                for model, submodel_list in zip(models, submodels):
                    for submodel in submodel_list:
                        engine_tasks.append(crawler.fetch_with_semaphore(crawler.get_engines_by_year_make_model_submodel, year, make, model, submodel))
        engines_by_submodel = await asyncio.gather(*engine_tasks)
        print("完成所有Engine的数据抓取")

        vio_tasks = []
        engine_index = 0
        for year, makes in zip(years_list, makes_by_year):
            for make in makes:
                models = models_by_make[engine_index]
                submodels = submodels_by_model[engine_index]
                engines = engines_by_submodel[engine_index]
                engine_index += 1
                for model, submodel_list, engine_list in zip(models, submodels, engines):
                    for submodel in submodel_list:
                        for engine in engine_list:
                            vio_tasks.append(crawler.fetch_with_semaphore(crawler.get_vio_by_year_make_model_submodel_engine, year, make, model, submodel, engine))
        await asyncio.gather(*vio_tasks)

        print(f"获取 VIO 数据完成")

    except Exception as e:
        traceback.print_exc()
    finally:
        await crawler.close()


if __name__ == '__main__':
    asyncio.run(main())
