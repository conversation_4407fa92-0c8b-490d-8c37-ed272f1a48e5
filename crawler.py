import aiohttp
import asyncio
import traceback
import pymongo
import logging
import time
from typing import List, Dict, Any, Optional
from lxml import etree
from dataclasses import dataclass


@dataclass
class RetryConfig:
    max_retries: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    backoff_factor: float = 2.0


class UACPartsCrawler:

    def __init__(self, concurrency=5, retry_config: Optional[RetryConfig] = None):
        self.headers = {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'no-cache',
            'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'origin': 'https://uacparts.com',
            'pragma': 'no-cache',
            'priority': 'u=1, i',
            'sec-ch-ua': '"Not A(Brand";v="8", "Chromium";v="132", "Google Chrome";v="132"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'x-requested-with': 'XMLHttpRequest'
        }

        # 配置连接池和超时
        timeout = aiohttp.ClientTimeout(total=30, connect=10)
        connector = aiohttp.TCPConnector(
            limit=100,  # 总连接池大小
            limit_per_host=20,  # 每个主机的连接数
            ttl_dns_cache=300,  # DNS缓存时间
            use_dns_cache=True,
        )

        self.session = aiohttp.ClientSession(
            headers=self.headers,
            timeout=timeout,
            connector=connector
        )
        self.semaphore = asyncio.Semaphore(concurrency)
        self.retry_config = retry_config or RetryConfig()

        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)

        # MongoDB连接
        self.mongo_obj = pymongo.MongoClient(
            "**************************************************************",
            maxPoolSize=50,
            minPoolSize=10,
            maxIdleTimeMS=30000,
            waitQueueTimeoutMS=5000
        )

    async def fetch_with_semaphore(self, func, *args):
        async with self.semaphore:
            return await func(*args)

    async def _make_request_with_retry(self, url: str, payload: Dict[str, Any],
                                     operation_name: str = "request") -> Dict[str, Any]:
        """带重试机制的HTTP请求"""
        last_exception = None

        for attempt in range(self.retry_config.max_retries + 1):
            try:
                async with self.session.post(url, data=payload) as response:
                    if response.status == 200:
                        result = await response.json()
                        if attempt > 0:
                            self.logger.info(f"{operation_name} 重试成功 (第{attempt + 1}次尝试)")
                        return result
                    else:
                        raise aiohttp.ClientResponseError(
                            request_info=response.request_info,
                            history=response.history,
                            status=response.status,
                            message=f"HTTP {response.status}"
                        )

            except Exception as e:
                last_exception = e
                if attempt < self.retry_config.max_retries:
                    delay = min(
                        self.retry_config.base_delay * (self.retry_config.backoff_factor ** attempt),
                        self.retry_config.max_delay
                    )
                    self.logger.warning(
                        f"{operation_name} 失败 (第{attempt + 1}次尝试): {str(e)}, "
                        f"{delay:.1f}秒后重试..."
                    )
                    await asyncio.sleep(delay)
                else:
                    self.logger.error(f"{operation_name} 最终失败: {str(e)}")

        raise last_exception

    async def get_all_years(self) -> List[Dict[str, Any]]:
        """获取所有年份数据"""
        url = "https://uacparts.com/VA_YearsAjaxDropdown/GetItems"
        payload = {
            "__aweconid": "VA_Years",
            "v": ""
        }

        year_list = await self._make_request_with_retry(url, payload, "获取年份数据")
        result = [year_data for year_data in year_list if year_data.get('k')]
        self.logger.info(f"获取到 {len(result)} 个年份")
        return result

    async def get_make_list_by_year(self, year: Dict[str, Any]) -> List[Dict[str, Any]]:
        """根据年份获取厂商列表"""
        url = "https://uacparts.com/VA_MakesByYearAjaxDropdown/GetItems"
        payload = {
            "__aweconid": "VA_MakesByYear",
            "v": "",
            "year": year['k']
        }

        make_list = await self._make_request_with_retry(
            url, payload, f"获取年份{year['c']}的厂商数据"
        )

        # 批量插入数据
        valid_makes = [data for data in make_list if data.get('k')]
        if valid_makes:
            documents = [{
                "yearid": year['k'],
                "year": year['c'],
                "makeid": data['k'],
                "make": data['c'],
            } for data in valid_makes]

            try:
                self.mongo_obj['UACParts0808']['MakeData'].insert_many(documents, ordered=False)
                self.logger.info(f"年份{year['c']}插入{len(documents)}个厂商数据")
            except pymongo.errors.BulkWriteError as e:
                self.logger.warning(f"批量插入厂商数据时出现重复: {len(e.details['writeErrors'])}个重复项")

        return valid_makes

    async def get_model_by_year_make(self, year: Dict[str, Any], make_id: Dict[str, Any]) -> List[Dict[str, Any]]:
        """根据年份和厂商获取车型列表"""
        url = "https://uacparts.com/VA_ModelsByYearMakeAjaxDropdown/GetItems"
        payload = {
            "__aweconid": "VA_ModelsByYearMake",
            "v": "",
            "year": year['k'],
            "make": make_id['k']
        }

        model_list = await self._make_request_with_retry(
            url, payload, f"获取{year['c']}-{make_id['c']}的车型数据"
        )

        # 批量插入数据
        valid_models = [data for data in model_list if data.get('k')]
        if valid_models:
            documents = [{
                "yearid": year['k'],
                "year": year['c'],
                "makeid": make_id['k'],
                "make": make_id['c'],
                "modelid": data['k'],
                "model": data['c'],
            } for data in valid_models]

            try:
                self.mongo_obj['UACParts0808']['ModelData'].insert_many(documents, ordered=False)
                self.logger.debug(f"{year['c']}-{make_id['c']}插入{len(documents)}个车型数据")
            except pymongo.errors.BulkWriteError as e:
                self.logger.warning(f"批量插入车型数据时出现重复: {len(e.details['writeErrors'])}个重复项")

        return valid_models

    async def get_submodel_by_year_make_model(self, year: Dict[str, Any], make_id: Dict[str, Any],
                                            model_id: Dict[str, Any]) -> List[Dict[str, Any]]:
        """根据年份、厂商和车型获取子车型列表"""
        url = "https://uacparts.com/VA_SubmodelsByYearMakeModelAjaxDropdown/GetItems"
        payload = {
            "__aweconid": "VA_SubmodelsByYearMakeModel",
            "v": "",
            "year": year['k'],
            "make": make_id['k'],
            "model": model_id['k']
        }

        submodel_list = await self._make_request_with_retry(
            url, payload, f"获取{year['c']}-{make_id['c']}-{model_id['c']}的子车型数据"
        )

        # 批量插入数据
        valid_submodels = [data for data in submodel_list if data.get('k')]
        if valid_submodels:
            documents = [{
                "yearid": year['k'],
                "year": year['c'],
                "makeid": make_id['k'],
                "make": make_id['c'],
                "modelid": model_id['k'],
                "model": model_id['c'],
                "submodelid": data['k'],
                "submodel": data['c'],
            } for data in valid_submodels]

            try:
                self.mongo_obj['UACParts0808']['SubModelData'].insert_many(documents, ordered=False)
                self.logger.debug(f"{year['c']}-{make_id['c']}-{model_id['c']}插入{len(documents)}个子车型数据")
            except pymongo.errors.BulkWriteError as e:
                self.logger.warning(f"批量插入子车型数据时出现重复: {len(e.details['writeErrors'])}个重复项")

        return valid_submodels

    async def get_engines_by_year_make_model_submodel(self, year: Dict[str, Any], make_id: Dict[str, Any],
                                                    model_id: Dict[str, Any], submodel_id: Dict[str, Any]) -> List[Dict[str, Any]]:
        """根据年份、厂商、车型和子车型获取引擎列表"""
        url = "https://uacparts.com/VA_EnginesByYearMakeModelSubAjaxDropdown/GetItems"
        payload = {
            "__aweconid": "VA_EnginesByYearMakeModelSub",
            "v": "",
            "year": year['k'],
            "make": make_id['k'],
            "model": model_id['k'],
            "submodel": submodel_id['k']
        }

        engines_list = await self._make_request_with_retry(
            url, payload, f"获取{year['c']}-{make_id['c']}-{model_id['c']}-{submodel_id['c']}的引擎数据"
        )

        # 批量插入数据
        valid_engines = [data for data in engines_list if data.get('k')]
        if valid_engines:
            documents = [{
                "yearid": year['k'],
                "year": year['c'],
                "makeid": make_id['k'],
                "make": make_id['c'],
                "modelid": model_id['k'],
                "model": model_id['c'],
                "submodelid": submodel_id['k'],
                "submodel": submodel_id['c'],
                "engineid": data['k'],
                "engine": data['c'],
            } for data in valid_engines]

            try:
                self.mongo_obj['UACParts0808']['EngineData'].insert_many(documents, ordered=False)
                self.logger.debug(f"{year['c']}-{make_id['c']}-{model_id['c']}-{submodel_id['c']}插入{len(documents)}个引擎数据")
            except pymongo.errors.BulkWriteError as e:
                self.logger.warning(f"批量插入引擎数据时出现重复: {len(e.details['writeErrors'])}个重复项")

        return valid_engines

    async def get_vio_by_year_make_model_submodel_engine(self, year: Dict[str, Any], make_id: Dict[str, Any],
                                                       model_id: Dict[str, Any], submodel_id: Dict[str, Any],
                                                       engine_id: Dict[str, Any]) -> None:
        """获取VIO数据"""
        url = "https://uacparts.com/Catalog/GetAppVIO"
        payload = {
            "yearid": year['k'],
            "makeid": make_id['k'],
            "modelid": model_id['k'],
            "submodelid": submodel_id['k'],
            "engineid": engine_id['k']
        }

        vio_data = await self._make_request_with_retry(
            url, payload, f"获取{year['c']}-{make_id['c']}-{model_id['c']}-{submodel_id['c']}-{engine_id['c']}的VIO数据"
        )

        document = {
            "yearid": year['k'],
            "year": year['c'],
            "makeid": make_id['k'],
            "make": make_id['c'],
            "modelid": model_id['k'],
            "model": model_id['c'],
            "submodelid": submodel_id['k'],
            "submodel": submodel_id['c'],
            "engineid": engine_id['k'],
            "engine": engine_id['c'],
            "vio": vio_data.get('VIO', '')
        }

        try:
            self.mongo_obj['UACParts0808']['VioData'].insert_one(document)
            self.logger.debug(f"插入VIO数据: {year['c']}-{make_id['c']}-{model_id['c']}-{submodel_id['c']}-{engine_id['c']}")
        except pymongo.errors.DuplicateKeyError:
            self.logger.warning(f"VIO数据已存在，跳过插入")
        except Exception as e:
            self.logger.error(f"插入VIO数据失败: {str(e)}")

    async def get_part_detail_by_all_args(self, year: Dict[str, Any], make_id: Dict[str, Any],
                                         model_id: Dict[str, Any], submodel_id: Dict[str, Any],
                                         engine_id: Dict[str, Any], vehicle_id: str) -> None:
        """获取零件详细信息"""
        url = "https://uacparts.com/PartsAjaxList/Search"
        payload = {
            "__aweconid": "Parts",
            "year": year['k'],
            "make": make_id['k'],
            "model": model_id['k'],
            "submodel": submodel_id['k'],
            "engine": engine_id['k'],
            "page": 1
        }

        parts_data = await self._make_request_with_retry(
            url, payload, f"获取{year['c']}-{make_id['c']}-{model_id['c']}-{submodel_id['c']}-{engine_id['c']}的零件数据"
        )

        is_more = parts_data.get("d", {}).get("m", None)
        parts_html_str = parts_data.get("d", {}).get("c", "")
        result_list = []

        if parts_html_str:
            try:
                html = etree.HTML(parts_html_str, parser=etree.HTMLParser(encoding='utf-8'))
                for li_ele in html.xpath("//li"):
                    try:
                        part_label_elements = li_ele.xpath(".//h2[@class='part-label']/text()")
                        if not part_label_elements:
                            continue

                        part_label = part_label_elements[0]
                        img_list = li_ele.xpath(".//div[@class='part-image']/img/@src")
                        part_desc = "\n".join(li_ele.xpath(".//div[@class='part-info']/div/text()"))

                        result_list.append({
                            "vehicle_id": vehicle_id,
                            "part_label": part_label,
                            "img_list": img_list,
                            "part_desc": part_desc,
                            "is_more": is_more
                        })
                    except Exception as e:
                        self.logger.warning(f"解析零件元素失败: {str(e)}")
                        continue

            except Exception as e:
                self.logger.error(f"解析零件HTML失败: {str(e)}")
                return

        if result_list:
            try:
                self.mongo_obj['UACParts0808']['PartsData'].insert_many(result_list, ordered=False)
                self.logger.debug(f"插入{len(result_list)}个零件数据")
            except Exception as e:
                self.logger.error(f"插入零件数据失败: {str(e)}")

    async def close(self):
        """关闭会话和数据库连接"""
        await self.session.close()
        self.mongo_obj.close()
        self.logger.info("爬虫会话已关闭")


async def main():
    # 配置重试参数
    retry_config = RetryConfig(
        max_retries=3,
        base_delay=1.0,
        max_delay=30.0,
        backoff_factor=2.0
    )

    crawler = UACPartsCrawler(concurrency=10, retry_config=retry_config)

    try:
        start_time = time.time()

        # 获取年份数据
        years_list = await crawler.get_all_years()
        crawler.logger.info(f"获取到 {len(years_list)} 个年份")

        # 获取厂商数据
        make_tasks = [crawler.fetch_with_semaphore(crawler.get_make_list_by_year, year) for year in years_list]
        makes_by_year = await asyncio.gather(*make_tasks, return_exceptions=True)

        # 处理异常结果
        valid_makes_by_year = []
        for i, result in enumerate(makes_by_year):
            if isinstance(result, Exception):
                crawler.logger.error(f"获取年份 {years_list[i]['c']} 的厂商数据失败: {str(result)}")
                valid_makes_by_year.append([])
            else:
                valid_makes_by_year.append(result)

        crawler.logger.info("完成所有厂商数据抓取")

        # 获取车型数据
        model_tasks = []
        year_make_pairs = []
        for year, makes in zip(years_list, valid_makes_by_year):
            for make in makes:
                model_tasks.append(crawler.fetch_with_semaphore(crawler.get_model_by_year_make, year, make))
                year_make_pairs.append((year, make))

        models_results = await asyncio.gather(*model_tasks, return_exceptions=True)
        crawler.logger.info("完成所有Model数据抓取")

        # 获取子车型数据
        submodel_tasks = []
        year_make_model_triplets = []
        for (year, make), models_result in zip(year_make_pairs, models_results):
            if isinstance(models_result, Exception):
                crawler.logger.error(f"获取 {year['c']}-{make['c']} 的车型数据失败: {str(models_result)}")
                continue

            for model in models_result:
                submodel_tasks.append(crawler.fetch_with_semaphore(crawler.get_submodel_by_year_make_model, year, make, model))
                year_make_model_triplets.append((year, make, model))

        submodels_results = await asyncio.gather(*submodel_tasks, return_exceptions=True)
        crawler.logger.info("完成所有SubModel的数据抓取")

        # 获取引擎数据
        engine_tasks = []
        year_make_model_submodel_quads = []
        for (year, make, model), submodels_result in zip(year_make_model_triplets, submodels_results):
            if isinstance(submodels_result, Exception):
                crawler.logger.error(f"获取 {year['c']}-{make['c']}-{model['c']} 的子车型数据失败: {str(submodels_result)}")
                continue

            for submodel in submodels_result:
                engine_tasks.append(crawler.fetch_with_semaphore(crawler.get_engines_by_year_make_model_submodel, year, make, model, submodel))
                year_make_model_submodel_quads.append((year, make, model, submodel))

        engines_results = await asyncio.gather(*engine_tasks, return_exceptions=True)
        crawler.logger.info("完成所有Engine的数据抓取")

        # 获取VIO数据
        vio_tasks = []
        for (year, make, model, submodel), engines_result in zip(year_make_model_submodel_quads, engines_results):
            if isinstance(engines_result, Exception):
                crawler.logger.error(f"获取 {year['c']}-{make['c']}-{model['c']}-{submodel['c']} 的引擎数据失败: {str(engines_result)}")
                continue

            for engine in engines_result:
                vio_tasks.append(crawler.fetch_with_semaphore(crawler.get_vio_by_year_make_model_submodel_engine, year, make, model, submodel, engine))

        await asyncio.gather(*vio_tasks, return_exceptions=True)

        end_time = time.time()
        crawler.logger.info(f"获取 VIO 数据完成，总耗时: {end_time - start_time:.2f}秒")

    except Exception as e:
        crawler.logger.error(f"爬虫执行过程中发生错误: {str(e)}")
        traceback.print_exc()
    finally:
        await crawler.close()


if __name__ == '__main__':
    asyncio.run(main())
